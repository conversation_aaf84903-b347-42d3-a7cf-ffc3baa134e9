import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { X } from "lucide-react";
import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { z } from "zod";
import { useActiveModifierGroups } from "../../hooks/useVariantsAndModifiers";
import type {
  CreateModifierRequest,
  Modifier,
  UpdateModifierRequest,
} from "../../types/api";
import { Button } from "../ui/Button";
import { Input } from "../ui/Input";
import { Select } from "../ui/Select";
import { Switch } from "../ui/Switch";

// Validation schema - will be created with translations in component
const createModifierSchema = (t: any) => z.object({
  groupId: z.string().min(1, t("validation.groupRequired")),
  name: z
    .string()
    .min(1, t("validation.nameRequired"))
    .max(100, t("validation.nameMaxLength", { max: 100 })),
  price: z.number().min(0, t("validation.priceNonNegative")).default(0),
  maxQuantity: z
    .number()
    .int()
    .min(1, t("validation.maxQuantityMinimum"))
    .default(1),
  inventoryItemId: z.string().optional(),
  displayOrder: z
    .number()
    .int()
    .min(0, t("validation.displayOrderNonNegative"))
    .default(0),
  active: z.boolean().default(true),
});

type ModifierFormData = z.infer<ReturnType<typeof createModifierSchema>>;

interface ModifierFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: CreateModifierRequest | UpdateModifierRequest) => void;
  modifier?: Modifier;
  defaultGroupId?: string;
  isLoading?: boolean;
}

export const ModifierForm: React.FC<ModifierFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
  modifier,
  defaultGroupId,
  isLoading = false,
}) => {
  const { t } = useTranslation();
  const isEditing = !!modifier;

  // Get modifier groups for select
  const { data: groupsResponse } = useActiveModifierGroups();
  const groups = groupsResponse?.data?.data || [];

  const modifierSchema = createModifierSchema(t);

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors, isValid },
  } = useForm<ModifierFormData>({
    resolver: zodResolver(modifierSchema),
    defaultValues: {
      groupId: defaultGroupId || "",
      name: "",
      price: 0,
      maxQuantity: 1,
      inventoryItemId: "",
      displayOrder: 0,
      active: true,
    },
  });

  // Form verilerini modifier'dan doldur
  useEffect(() => {
    if (modifier) {
      setValue("groupId", modifier.groupId);
      setValue("name", modifier.name);
      setValue("price", modifier.price);
      setValue("maxQuantity", modifier.maxQuantity);
      setValue("inventoryItemId", modifier.inventoryItemId || "");
      setValue("displayOrder", modifier.displayOrder);
      setValue("active", modifier.active);
    } else {
      reset({
        groupId: defaultGroupId || "",
        name: "",
        price: 0,
        maxQuantity: 1,
        inventoryItemId: "",
        displayOrder: 0,
        active: true,
      });
    }
  }, [modifier, defaultGroupId, setValue, reset]);

  const handleFormSubmit = (data: ModifierFormData) => {
    console.log("Form data before submit:", data);

    // inventoryItemId boş string ise undefined yap
    const cleanedData = {
      ...data,
      inventoryItemId: data.inventoryItemId === "" ? undefined : data.inventoryItemId
    };

    console.log("Cleaned data:", cleanedData);

    if (isEditing) {
      const { groupId: _, ...updateData } = cleanedData;
      onSubmit(updateData);
    } else {
      onSubmit(cleanedData);
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {isEditing
              ? t("modifiers.editModifier")
              : t("modifiers.addModifier")}
          </h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Form */}
        <form
          onSubmit={handleSubmit(handleFormSubmit)}
          className="p-6 space-y-6"
        >
          {/* Grup Seçimi */}
          {!isEditing && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t("modifiers.group")} *
              </label>
              <Select
                {...register("groupId")}
                placeholder={t("modifiers.group")}
                error={errors.groupId?.message}
                className="bg-neutral-800 dark:bg-neutral-800"
                options={[
                  { value: "", label: `${t("modifiers.group")} seçin` },
                  ...groups.map(group => ({
                    value: group.id,
                    label: group.name
                  }))
                ]}
                value={watch("groupId")}
                onChange={(value) => setValue("groupId", value)}
              />
            </div>
          )}

          {/* Modifiye Adı */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t("modifiers.modifierName")} *
            </label>
            <Input
              {...register("name")}
              placeholder={t("modifiers.modifierName")}
              error={errors.name?.message}
              className="bg-[#292C2D] dark:bg-[#292C2D]"
            />
          </div>

          {/* Fiyat ve Maksimum Miktar */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t("modifiers.price")} (₺)
              </label>
              <Input
                {...register("price", { valueAsNumber: true })}
                type="number"
                step="0.01"
                min="0"
                placeholder="0.00"
                error={errors.price?.message}
                className="bg-[#292C2D] dark:bg-[#292C2D]"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t("modifiers.maxQuantity")}
              </label>
              <Input
                {...register("maxQuantity", { valueAsNumber: true })}
                type="number"
                min="1"
                placeholder="1"
                error={errors.maxQuantity?.message}
                className="bg-[#292C2D] dark:bg-[#292C2D]"
              />
            </div>
          </div>

          {/* Envanter Öğesi */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t("modifiers.inventoryItem")}
            </label>
            <Select
              {...register("inventoryItemId")}
              placeholder={t("modifiers.inventoryItem")}
              error={errors.inventoryItemId?.message}
              className="bg-neutral-800 dark:bg-neutral-800"
              options={[
                { value: "", label: `${t("modifiers.inventoryItem")} seçin (opsiyonel)` }
                // TODO: Envanter öğelerini buraya ekle
              ]}
              value={watch("inventoryItemId")}
              onChange={(value) => setValue("inventoryItemId", value)}
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Stok takibi için envanter öğesi seçebilirsiniz
            </p>
          </div>

          {/* Görüntüleme Sırası */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t("modifiers.displayOrder")}
            </label>
            <Input
              {...register("displayOrder", { valueAsNumber: true })}
              type="number"
              min="0"
              placeholder="0"
              error={errors.displayOrder?.message}
              className="bg-[#292C2D] dark:bg-[#292C2D]"
            />
          </div>

          {/* Aktif Durumu */}
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("modifiers.active")}
            </label>
            <Switch
              checked={watch("active")}
              onCheckedChange={checked => setValue("active", checked)}
            />
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              {t("common.cancel")}
            </Button>
            <Button
              type="submit"
              disabled={!isValid || isLoading}
              loading={isLoading}
            >
              {isEditing ? t("common.update") : t("common.add")}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};
